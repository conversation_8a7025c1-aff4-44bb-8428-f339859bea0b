<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/content_background"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Emergency Stop Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:cardBackgroundColor="@color/status_disconnected"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="⚠️ Emergency Override"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <Button
                    android:id="@+id/btn_emergency_stop"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🛑 STOP ALL"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:backgroundTint="@android:color/holo_red_dark"
                    android:textColor="@android:color/white"
                    android:padding="8dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Movement Control Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Movement Control"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <!-- Speed Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Speed:"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:layout_marginEnd="6dp" />

                    <SeekBar
                        android:id="@+id/speed_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="255"
                        android:progress="150" />

                    <TextView
                        android:id="@+id/speed_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="150"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        android:layout_marginStart="6dp"
                        android:minWidth="35dp" />

                </LinearLayout>

                <!-- Movement Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center">

                    <!-- Forward Button -->
                    <Button
                        android:id="@+id/btn_forward"
                        android:layout_width="60dp"
                        android:layout_height="45dp"
                        android:text="↑"
                        android:textSize="18sp"
                        android:backgroundTint="@color/robot_primary"
                        android:layout_marginBottom="6dp" />

                    <!-- Left, Stop, Right Buttons -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_left"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:text="←"
                            android:textSize="24sp"
                            android:backgroundTint="@color/robot_primary"
                            android:layout_marginEnd="8dp" />

                        <Button
                            android:id="@+id/btn_stop"
                            android:layout_width="80dp"
                            android:layout_height="60dp"
                            android:text="STOP"
                            android:textSize="12sp"
                            android:backgroundTint="@color/status_disconnected"
                            android:layout_marginHorizontal="8dp" />

                        <Button
                            android:id="@+id/btn_right"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:text="→"
                            android:textSize="24sp"
                            android:backgroundTint="@color/robot_primary"
                            android:layout_marginStart="8dp" />

                    </LinearLayout>

                    <!-- Backward Button -->
                    <Button
                        android:id="@+id/btn_backward"
                        android:layout_width="80dp"
                        android:layout_height="60dp"
                        android:text="↓"
                        android:textSize="24sp"
                        android:backgroundTint="@color/robot_primary"
                        android:layout_marginTop="8dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Mecanum Wheel Omnidirectional Control Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔄 Mecanum Wheel Control"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <!-- Slide Controls -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="8dp">

                    <Button
                        android:id="@+id/btn_slide_left"
                        android:layout_width="80dp"
                        android:layout_height="45dp"
                        android:text="⬅️ SL"
                        android:textSize="10sp"
                        android:backgroundTint="@color/robot_secondary"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/btn_slide_right"
                        android:layout_width="80dp"
                        android:layout_height="45dp"
                        android:text="➡️ SR"
                        android:textSize="10sp"
                        android:backgroundTint="@color/robot_secondary"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Diagonal Controls -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="8dp">

                    <Button
                        android:id="@+id/btn_diagonal_front_left"
                        android:layout_width="70dp"
                        android:layout_height="40dp"
                        android:text="↖️ DFL"
                        android:textSize="8sp"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginEnd="4dp" />

                    <Button
                        android:id="@+id/btn_diagonal_front_right"
                        android:layout_width="70dp"
                        android:layout_height="40dp"
                        android:text="↗️ DFR"
                        android:textSize="8sp"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginHorizontal="4dp" />

                    <Button
                        android:id="@+id/btn_diagonal_back_left"
                        android:layout_width="70dp"
                        android:layout_height="40dp"
                        android:text="↙️ DBL"
                        android:textSize="8sp"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginHorizontal="4dp" />

                    <Button
                        android:id="@+id/btn_diagonal_back_right"
                        android:layout_width="70dp"
                        android:layout_height="40dp"
                        android:text="↘️ DBR"
                        android:textSize="8sp"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginStart="4dp" />

                </LinearLayout>

                <!-- Rotation Controls -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <Button
                        android:id="@+id/btn_rotate_left"
                        android:layout_width="80dp"
                        android:layout_height="45dp"
                        android:text="🔄 ROT L"
                        android:textSize="9sp"
                        android:backgroundTint="@color/status_warning"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/btn_rotate_right"
                        android:layout_width="80dp"
                        android:layout_height="45dp"
                        android:text="🔄 ROT R"
                        android:textSize="9sp"
                        android:backgroundTint="@color/status_warning"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Arm Control Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/arm_control_title"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Left Shoulder Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:text="Left Shoulder:"
                        android:textColor="@color/text_secondary" />

                    <SeekBar
                        android:id="@+id/left_shoulder_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="180"
                        android:progress="180" />

                    <TextView
                        android:id="@+id/left_shoulder_value"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:text="180°"
                        android:textColor="@color/text_primary"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Right Shoulder Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:text="Right Shoulder:"
                        android:textColor="@color/text_secondary" />

                    <SeekBar
                        android:id="@+id/right_shoulder_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="180"
                        android:progress="0" />

                    <TextView
                        android:id="@+id/right_shoulder_value"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:text="0°"
                        android:textColor="@color/text_primary"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Gesture Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <Button
                        android:id="@+id/btn_wave"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/wave_gesture"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginEnd="4dp" />

                    <Button
                        android:id="@+id/btn_point"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/point_gesture"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginHorizontal="4dp" />

                    <Button
                        android:id="@+id/btn_rest"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/rest_position"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginStart="4dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Head Control Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/head_control_title"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Head Pan Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="Pan:"
                        android:textColor="@color/text_secondary" />

                    <SeekBar
                        android:id="@+id/head_pan_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="180"
                        android:progress="90" />

                    <TextView
                        android:id="@+id/head_pan_value"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:text="90°"
                        android:textColor="@color/text_primary"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Head Tilt Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="Tilt:"
                        android:textColor="@color/text_secondary" />

                    <SeekBar
                        android:id="@+id/head_tilt_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="180"
                        android:progress="90" />

                    <TextView
                        android:id="@+id/head_tilt_value"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:text="90°"
                        android:textColor="@color/text_primary"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Head Movement Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="8dp">

                    <Button
                        android:id="@+id/btn_look_left"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Look Left"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginEnd="4dp" />

                    <Button
                        android:id="@+id/btn_center_head"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/center_head"
                        android:backgroundTint="@color/robot_primary"
                        android:layout_marginHorizontal="4dp" />

                    <Button
                        android:id="@+id/btn_look_right"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Look Right"
                        android:backgroundTint="@color/robot_accent"
                        android:layout_marginStart="4dp" />

                </LinearLayout>

                <!-- Movement Speed Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="16dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:text="Movement Speed:"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/movement_speed_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="45"
                        android:progress="10" />

                    <TextView
                        android:id="@+id/movement_speed_value"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="15ms"
                        android:textColor="@color/text_primary"
                        android:gravity="center"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Human Detection Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Human Detection &amp; Greeting"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Distance Display -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Distance:"
                        android:textColor="@color/text_secondary"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:id="@+id/ultrasonic_distance_value"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="-- cm"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/btn_get_distance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Update"
                        android:textSize="12sp"
                        android:backgroundTint="@color/robot_accent"
                        android:padding="8dp" />

                </LinearLayout>

                <!-- Note: Smart Greeting is now handled by VisionFragment with face detection -->
                <!-- Ultrasonic distance is displayed above for monitoring purposes -->

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
