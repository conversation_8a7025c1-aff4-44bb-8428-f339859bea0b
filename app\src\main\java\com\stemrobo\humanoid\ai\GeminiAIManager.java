package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class GeminiAIManager {
    private static final String TAG = "GeminiAIManager";
    
    // Replace with your actual Gemini API key
    private static final String GEMINI_API_KEY = "AIzaSyAZZ_pO5Mx7_M7Kbrfi21h77xfyMNziPnA";
    private static final String GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
    
    private Context context;
    private OkHttpClient httpClient;
    private ExecutorService executorService;
    
    // AI Response callback
    public interface AIResponseCallback {
        void onResponse(String response);
        void onError(String error);
    }
    
    // Robot action callback
    public interface RobotActionCallback {
        void executeMovement(String direction, int speed, int duration);
        void executeGesture(String gesture);
        void executeHeadMovement(int pan, int tilt);
        void speak(String text);
    }
    
    private RobotActionCallback robotActionCallback;
    
    public GeminiAIManager(Context context) {
        this.context = context;
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
            .build();
        this.executorService = Executors.newSingleThreadExecutor();
    }
    
    public void setRobotActionCallback(RobotActionCallback callback) {
        this.robotActionCallback = callback;
    }
    
    public void processUserInput(String userInput, AIResponseCallback callback) {
        if (GEMINI_API_KEY.equals("YOUR_GEMINI_API_KEY_HERE")) {
            // Fallback to local processing if no API key
            processLocalAI(userInput, callback);
            return;
        }
        
        executorService.execute(() -> {
            try {
                String prompt = createRobotPrompt(userInput);
                sendGeminiRequest(prompt, callback);
            } catch (Exception e) {
                Log.e(TAG, "Error processing user input", e);
                callback.onError("Error processing request: " + e.getMessage());
            }
        });
    }
    
    private String createRobotPrompt(String userInput) {
        return "You are Guruji, a friendly humanoid robot assistant created by STEM-Xpert. " +
               "You can move (forward, backward, left, right), perform gestures (wave, point, rest), " +
               "move your head (pan and tilt), and speak responses. " +
               "User said: \"" + userInput + "\"\n\n" +
               "Respond with a JSON object containing:\n" +
               "- \"speech\": what you should say to the user\n" +
               "- \"actions\": array of actions to perform (movement, gesture, head_movement)\n" +
               "- \"reasoning\": brief explanation of your response\n\n" +
               "Example actions:\n" +
               "{\"type\": \"movement\", \"direction\": \"forward\", \"speed\": 150, \"duration\": 2000}\n" +
               "{\"type\": \"gesture\", \"name\": \"wave\"}\n" +
               "{\"type\": \"head_movement\", \"pan\": 45, \"tilt\": 0}\n" +
               "{\"type\": \"speech\", \"text\": \"Hello there!\"}\n\n" +
               "Keep responses friendly, helpful, and appropriate for a STEM educational robot.";
    }
    
    private void sendGeminiRequest(String prompt, AIResponseCallback callback) {
        try {
            JSONObject requestJson = new JSONObject();
            JSONArray contents = new JSONArray();
            JSONObject content = new JSONObject();
            JSONArray parts = new JSONArray();
            JSONObject part = new JSONObject();
            
            part.put("text", prompt);
            parts.put(part);
            content.put("parts", parts);
            contents.put(content);
            requestJson.put("contents", contents);
            
            // Add safety settings
            JSONArray safetySettings = new JSONArray();
            addSafetySetting(safetySettings, "HARM_CATEGORY_HARASSMENT", "BLOCK_MEDIUM_AND_ABOVE");
            addSafetySetting(safetySettings, "HARM_CATEGORY_HATE_SPEECH", "BLOCK_MEDIUM_AND_ABOVE");
            addSafetySetting(safetySettings, "HARM_CATEGORY_SEXUALLY_EXPLICIT", "BLOCK_MEDIUM_AND_ABOVE");
            addSafetySetting(safetySettings, "HARM_CATEGORY_DANGEROUS_CONTENT", "BLOCK_MEDIUM_AND_ABOVE");
            requestJson.put("safetySettings", safetySettings);
            
            RequestBody body = RequestBody.create(
                requestJson.toString(),
                MediaType.get("application/json")
            );
            
            Request request = new Request.Builder()
                .url(GEMINI_API_URL + "?key=" + GEMINI_API_KEY)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
            
            httpClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "Gemini API request failed", e);
                    callback.onError("Network error: " + e.getMessage());
                }
                
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        
                        if (response.isSuccessful()) {
                            String aiResponse = parseGeminiResponse(responseBody);
                            processAIResponse(aiResponse);
                            callback.onResponse(aiResponse);
                        } else {
                            Log.e(TAG, "Gemini API error: " + response.code() + " - " + responseBody);
                            callback.onError("API error: " + response.code());
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error parsing Gemini response", e);
                        callback.onError("Error parsing response: " + e.getMessage());
                    }
                }
            });
            
        } catch (JSONException e) {
            Log.e(TAG, "Error creating Gemini request", e);
            callback.onError("Error creating request: " + e.getMessage());
        }
    }
    
    private void addSafetySetting(JSONArray safetySettings, String category, String threshold) throws JSONException {
        JSONObject setting = new JSONObject();
        setting.put("category", category);
        setting.put("threshold", threshold);
        safetySettings.put(setting);
    }
    
    private String parseGeminiResponse(String responseBody) throws JSONException {
        JSONObject response = new JSONObject(responseBody);
        JSONArray candidates = response.getJSONArray("candidates");
        
        if (candidates.length() > 0) {
            JSONObject candidate = candidates.getJSONObject(0);
            JSONObject content = candidate.getJSONObject("content");
            JSONArray parts = content.getJSONArray("parts");
            
            if (parts.length() > 0) {
                JSONObject part = parts.getJSONObject(0);
                return part.getString("text");
            }
        }
        
        return "I'm sorry, I couldn't process that request.";
    }
    
    private void processAIResponse(String aiResponse) {
        try {
            // Try to parse as JSON for structured responses
            JSONObject response = new JSONObject(aiResponse);
            
            // Execute speech
            if (response.has("speech")) {
                String speech = response.getString("speech");
                if (robotActionCallback != null) {
                    robotActionCallback.speak(speech);
                }
            }
            
            // Execute actions
            if (response.has("actions")) {
                JSONArray actions = response.getJSONArray("actions");
                for (int i = 0; i < actions.length(); i++) {
                    JSONObject action = actions.getJSONObject(i);
                    executeAction(action);
                }
            }
            
        } catch (JSONException e) {
            // If not JSON, treat as plain text speech
            if (robotActionCallback != null) {
                robotActionCallback.speak(aiResponse);
            }
        }
    }
    
    private void executeAction(JSONObject action) throws JSONException {
        if (robotActionCallback == null) return;
        
        String type = action.getString("type");
        
        switch (type) {
            case "movement":
                String direction = action.getString("direction");
                int speed = action.optInt("speed", 150);
                int duration = action.optInt("duration", 2000);
                robotActionCallback.executeMovement(direction, speed, duration);
                break;
                
            case "gesture":
                String gesture = action.getString("name");
                robotActionCallback.executeGesture(gesture);
                break;
                
            case "head_movement":
                int pan = action.optInt("pan", 90);
                int tilt = action.optInt("tilt", 90);
                robotActionCallback.executeHeadMovement(pan, tilt);
                break;
                
            case "speech":
                String text = action.getString("text");
                robotActionCallback.speak(text);
                break;
        }
    }
    
    private void processLocalAI(String userInput, AIResponseCallback callback) {
        // Simple local AI processing when Gemini API is not available
        String response = generateLocalResponse(userInput.toLowerCase());
        
        // Execute simple actions based on keywords
        executeLocalActions(userInput.toLowerCase());
        
        callback.onResponse(response);
    }
    
    private String generateLocalResponse(String input) {
        if (input.contains("hello") || input.contains("hi")) {
            return "Hello! I'm STEM Robot. How can I help you today?";
        } else if (input.contains("move") || input.contains("forward")) {
            return "Moving forward as requested!";
        } else if (input.contains("wave")) {
            return "Waving hello to you!";
        } else if (input.contains("dance")) {
            return "Let me show you some moves!";
        } else if (input.contains("turn")) {
            return "Turning as requested!";
        } else if (input.contains("stop")) {
            return "Stopping all movement.";
        } else if (input.contains("thank")) {
            return "You're welcome! I'm happy to help.";
        } else if (input.contains("name")) {
            return "I'm STEM Robot, your friendly humanoid assistant!";
        } else {
            return "I understand you said: " + input + ". Let me help you with that!";
        }
    }
    
    private void executeLocalActions(String input) {
        if (robotActionCallback == null) return;
        
        if (input.contains("move forward") || input.contains("go forward") || input.contains("forward")) {
            robotActionCallback.executeMovement("F", 150, 0); // 0 = use preference setting
        } else if (input.contains("move backward") || input.contains("go backward") || input.contains("backward")) {
            robotActionCallback.executeMovement("B", 150, 0); // 0 = use preference setting
        } else if (input.contains("turn left") || input.contains("left")) {
            robotActionCallback.executeMovement("L", 150, 0); // 0 = use preference setting
        } else if (input.contains("turn right") || input.contains("right")) {
            robotActionCallback.executeMovement("R", 150, 0); // 0 = use preference setting
        } else if (input.contains("stop")) {
            robotActionCallback.executeMovement("S", 150, 500); // Stop should be immediate
        } else if (input.contains("wave")) {
            robotActionCallback.executeGesture("wave");
        } else if (input.contains("point")) {
            robotActionCallback.executeGesture("point");
        } else if (input.contains("dance")) {
            // Execute a dance sequence
            robotActionCallback.executeGesture("wave");
            robotActionCallback.executeHeadMovement(45, 90);
            robotActionCallback.executeHeadMovement(135, 90);
            robotActionCallback.executeHeadMovement(90, 90);
        } else if (input.contains("look left")) {
            robotActionCallback.executeHeadMovement(45, 90);
        } else if (input.contains("look right")) {
            robotActionCallback.executeHeadMovement(135, 90);
        } else if (input.contains("look up")) {
            robotActionCallback.executeHeadMovement(90, 45);
        } else if (input.contains("look down")) {
            robotActionCallback.executeHeadMovement(90, 135);
        } else if (input.contains("center") || input.contains("straight")) {
            robotActionCallback.executeHeadMovement(90, 90);
        }
    }
    
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
