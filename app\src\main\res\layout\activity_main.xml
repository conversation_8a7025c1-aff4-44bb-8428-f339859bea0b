<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/robot_background"
    tools:context=".MainActivity">

    <!-- Robot Status Area (Chest Display) -->
    <LinearLayout
        android:id="@+id/robot_status_container"
        android:layout_width="0dp"
        android:layout_height="80dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/robot_face_background"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:padding="8dp">

        <!-- Robot Status Text -->
        <TextView
            android:id="@+id/robot_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="STEM ROBOT"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginEnd="16dp" />

        <!-- Heart Rate Display (only visible when sensor touched) -->
        <TextView
            android:id="@+id/heart_rate_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@color/status_connected"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="gone"
            android:layout_marginHorizontal="8dp" />

        <!-- Smart Greeting Status Displays -->
        <TextView
            android:id="@+id/face_count_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Faces: 0"
            android:textColor="@color/text_secondary"
            android:textSize="10sp"
            android:visibility="visible"
            android:layout_marginHorizontal="4dp" />

        <TextView
            android:id="@+id/distance_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Distance: --.-cm"
            android:textColor="@color/text_secondary"
            android:textSize="10sp"
            android:visibility="gone"
            android:layout_marginHorizontal="4dp" />

        <!-- Small Camera Preview for Smart Greeting -->
        <androidx.camera.view.PreviewView
            android:id="@+id/mini_camera_preview"
            android:layout_width="60dp"
            android:layout_height="45dp"
            android:layout_marginHorizontal="4dp"
            android:visibility="visible"
            android:background="@drawable/robot_face_background"
            android:scaleType="centerCrop" />

        <!-- System Status Indicator -->
        <TextView
            android:id="@+id/system_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ONLINE"
            android:textColor="@color/status_connected"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginStart="16dp" />

    </LinearLayout>

    <!-- Status Bar -->
    <LinearLayout
        android:id="@+id/status_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="4dp"
        android:background="@color/status_bar_background"
        app:layout_constraintTop_toBottomOf="@id/robot_status_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Connection Mode Indicator -->
        <TextView
            android:id="@+id/connection_mode_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="WiFi: ●"
            android:textColor="@color/status_disconnected"
            android:textSize="10sp"
            android:gravity="center" />

        <!-- Connection Status Indicators -->
        <TextView
            android:id="@+id/motor_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Motor: ●"
            android:textColor="@color/status_disconnected"
            android:textSize="10sp"
            android:gravity="center" />

        <TextView
            android:id="@+id/servo_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Servo: ●"
            android:textColor="@color/status_disconnected"
            android:textSize="10sp"
            android:gravity="center" />

        <TextView
            android:id="@+id/sensor_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Sensor: ●"
            android:textColor="@color/status_disconnected"
            android:textSize="10sp"
            android:gravity="center" />

        <TextView
            android:id="@+id/voice_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Voice: ●"
            android:textColor="@color/status_disconnected"
            android:textSize="10sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Main Content Area -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/status_bar"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/content_background" />

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        style="@style/CompactBottomNavigationView"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:background="@color/navigation_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:menu="@menu/bottom_navigation_menu"
        app:itemIconTint="@color/navigation_item_color"
        app:itemTextColor="@color/navigation_item_color"
        app:labelVisibilityMode="labeled" />

    <!-- Full-Screen Screen Saver Overlay -->
    <FrameLayout
        android:id="@+id/screen_saver_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#000000"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        android:elevation="2000dp"
        android:fitsSystemWindows="false">

        <!-- Video Player for Screen Saver - Full Screen Coverage -->
        <android.widget.VideoView
            android:id="@+id/screen_saver_video"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:keepScreenOn="true" />

        <!-- Minimal touch to exit instruction -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="20dp"
            android:text="Touch to exit"
            android:textColor="#FFFFFF"
            android:textSize="10sp"
            android:alpha="0.3"
            android:padding="4dp" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
